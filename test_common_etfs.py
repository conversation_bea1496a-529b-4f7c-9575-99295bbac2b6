"""
测试常见A股ETF数据获取
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta

def test_common_etfs():
    """测试常见的A股ETF"""
    print("测试常见A股ETF数据获取")
    print("="*50)
    
    # 常见的A股ETF列表
    common_etfs = {
        '510050': '50ETF',
        '510300': '300ETF', 
        '510500': '500ETF',
        '159915': '创业板ETF',
        '159919': '300ETF',
        '159949': '创业板50',
        '512100': '1000ETF',
        '515050': '5GETF',
        '516160': '新能源ETF',
        '159995': '芯片ETF',
        '159928': '消费ETF',
        '512880': '证券ETF',
        '512690': '酒ETF',
        '159941': '纳指ETF',
        '513100': '纳指ETF',
        '513500': '标普500',
        '159920': '恒生ETF',
        '513330': '恒生科技ETF'  # 再次尝试
    }
    
    successful_etfs = []
    failed_etfs = []
    
    # 设置较短的时间范围进行测试
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
    
    print(f"测试时间范围: {start_date} 到 {end_date}")
    print()
    
    for etf_code, etf_name in common_etfs.items():
        try:
            print(f"测试 {etf_code}({etf_name})...", end=" ")
            
            data = ak.stock_zh_a_hist(
                symbol=etf_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=""
            )
            
            if not data.empty:
                print(f"✓ 成功获取 {len(data)} 条记录")
                successful_etfs.append((etf_code, etf_name, len(data)))
                
                # 显示最新几条数据
                if len(data) > 0:
                    latest_data = data.tail(1)
                    if '日期' in latest_data.columns and '收盘' in latest_data.columns:
                        latest_date = latest_data['日期'].iloc[0]
                        latest_close = latest_data['收盘'].iloc[0]
                        print(f"    最新数据: {latest_date}, 收盘价: {latest_close}")
            else:
                print("✗ 未获取到数据")
                failed_etfs.append((etf_code, etf_name))
                
        except Exception as e:
            print(f"✗ 出错: {e}")
            failed_etfs.append((etf_code, etf_name))
        
        # 添加延迟避免请求过快
        import time
        time.sleep(0.5)
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"成功获取数据的ETF ({len(successful_etfs)}):")
    for etf_code, etf_name, count in successful_etfs:
        print(f"  {etf_code}({etf_name}): {count} 条记录")
    
    print(f"\n未能获取数据的ETF ({len(failed_etfs)}):")
    for etf_code, etf_name in failed_etfs:
        print(f"  {etf_code}({etf_name})")
    
    # 如果有成功的ETF，选择一个进行详细测试
    if successful_etfs:
        test_etf_code, test_etf_name, _ = successful_etfs[0]
        print(f"\n详细测试 {test_etf_code}({test_etf_name}):")
        
        # 获取更长时间范围的数据
        long_start_date = "20240101"
        
        try:
            detailed_data = ak.stock_zh_a_hist(
                symbol=test_etf_code,
                period="daily",
                start_date=long_start_date,
                end_date=end_date,
                adjust=""
            )
            
            if not detailed_data.empty:
                print(f"获取到 {len(detailed_data)} 条详细数据")
                print("数据列名:", list(detailed_data.columns))
                print("数据预览:")
                print(detailed_data.head())
                print("\n最新数据:")
                print(detailed_data.tail())
                
                # 保存数据用于后续回测
                filename = f'etf_{test_etf_code}_data.csv'
                detailed_data.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"\n数据已保存到 {filename}")
                
                return test_etf_code, test_etf_name, detailed_data
            
        except Exception as e:
            print(f"获取详细数据时出错: {e}")
    
    return None, None, pd.DataFrame()


def prepare_data_for_backtest(etf_code: str, data: pd.DataFrame) -> pd.DataFrame:
    """为回测准备数据"""
    if data.empty:
        return pd.DataFrame()
    
    # 重命名列以符合pybroker要求
    data_clean = data.rename(columns={
        '日期': 'date',
        '开盘': 'open',
        '收盘': 'close',
        '最高': 'high',
        '最低': 'low',
        '成交量': 'volume'
    })
    
    # 确保数据类型正确
    data_clean['date'] = pd.to_datetime(data_clean['date'])
    data_clean['symbol'] = etf_code
    
    # 选择需要的列
    required_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']
    data_clean = data_clean[required_columns]
    
    # 按日期排序
    data_clean = data_clean.sort_values('date').reset_index(drop=True)
    
    print(f"数据清理完成:")
    print(f"  时间范围: {data_clean['date'].min()} 到 {data_clean['date'].max()}")
    print(f"  数据点数: {len(data_clean)}")
    print(f"  价格范围: {data_clean['close'].min():.2f} - {data_clean['close'].max():.2f}")
    
    return data_clean


if __name__ == "__main__":
    etf_code, etf_name, data = test_common_etfs()
    
    if etf_code and not data.empty:
        print(f"\n为 {etf_code}({etf_name}) 准备回测数据...")
        clean_data = prepare_data_for_backtest(etf_code, data)
        
        if not clean_data.empty:
            # 保存清理后的数据
            clean_filename = f'clean_etf_{etf_code}_data.csv'
            clean_data.to_csv(clean_filename, index=False, encoding='utf-8-sig')
            print(f"清理后的数据已保存到 {clean_filename}")
            
            print(f"\n可以使用 {etf_code} 进行回测了！")
        else:
            print("数据清理失败")
    else:
        print("\n未找到可用的ETF数据进行回测")
