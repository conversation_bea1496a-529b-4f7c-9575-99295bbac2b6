"""
A股ETF数据获取模块 - 使用AKShare获取恒生科技ETF(513330)数据
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional
import time


class AShareETFDataLoader:
    """A股ETF数据加载器"""
    
    def __init__(self):
        self.etf_code = "513330"  # 恒生科技ETF
        self.etf_name = "恒生科技ETF"
        
    def fetch_etf_data(self, 
                      symbol: str = "513330", 
                      start_date: str = "20240101", 
                      end_date: Optional[str] = None,
                      period: str = "daily") -> pd.DataFrame:
        """
        获取A股ETF历史数据
        
        Args:
            symbol: ETF代码，默认513330(恒生科技ETF)
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD，默认为今天
            period: 数据频率，默认daily
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")
            
        try:
            print(f"正在获取 {symbol}({self.etf_name}) 的历史数据...")
            print(f"数据范围: {start_date} 到 {end_date}")
            
            # 使用akshare获取A股历史数据
            # 注意：A股ETF使用stock_zh_a_hist函数
            data = ak.stock_zh_a_hist(
                symbol=symbol,
                period=period,
                start_date=start_date,
                end_date=end_date,
                adjust=""  # 不复权
            )
            
            if data.empty:
                raise ValueError(f"无法获取 {symbol} 的数据")
            
            # 重命名列以符合pybroker的要求
            data = data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close', 
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_chg',
                '涨跌额': 'change',
                '换手率': 'turnover'
            })
            
            # 确保日期列是datetime类型
            data['date'] = pd.to_datetime(data['date'])
            
            # 添加symbol列
            data['symbol'] = symbol
            
            # 选择需要的列
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']
            data = data[required_columns]
            
            # 按日期排序
            data = data.sort_values('date').reset_index(drop=True)
            
            print(f"成功获取 {symbol} 数据: {len(data)} 条记录")
            print(f"数据范围: {data['date'].min()} 到 {data['date'].max()}")
            
            return data
            
        except Exception as e:
            print(f"获取 {symbol} 数据时出错: {e}")
            return pd.DataFrame()
    
    def get_etf_info(self, symbol: str = "513330") -> dict:
        """
        获取ETF基本信息

        Args:
            symbol: ETF代码

        Returns:
            ETF基本信息字典
        """
        try:
            # 获取所有A股实时数据，然后筛选
            spot_data = ak.stock_zh_a_spot_em()

            if not spot_data.empty:
                # 筛选指定代码的数据
                etf_data = spot_data[spot_data['代码'] == symbol]

                if not etf_data.empty:
                    info = {
                        'code': symbol,
                        'name': etf_data.iloc[0]['名称'] if '名称' in etf_data.columns else self.etf_name,
                        'current_price': etf_data.iloc[0]['最新价'] if '最新价' in etf_data.columns else None,
                        'change_pct': etf_data.iloc[0]['涨跌幅'] if '涨跌幅' in etf_data.columns else None,
                        'volume': etf_data.iloc[0]['成交量'] if '成交量' in etf_data.columns else None,
                        'market_cap': etf_data.iloc[0]['总市值'] if '总市值' in etf_data.columns else None,
                    }
                    return info
                else:
                    return {'code': symbol, 'name': self.etf_name}
            else:
                return {'code': symbol, 'name': self.etf_name}

        except Exception as e:
            print(f"获取ETF信息时出错: {e}")
            return {'code': symbol, 'name': self.etf_name}
    
    def save_data(self, data: pd.DataFrame, filename: str = 'etf_513330_data.csv'):
        """保存数据到CSV文件"""
        try:
            data.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存数据时出错: {e}")
    
    def load_data(self, filename: str = 'etf_513330_data.csv') -> pd.DataFrame:
        """从CSV文件加载数据"""
        try:
            data = pd.read_csv(filename, encoding='utf-8-sig')
            data['date'] = pd.to_datetime(data['date'])
            print(f"从 {filename} 加载了 {len(data)} 条数据记录")
            return data
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
            return pd.DataFrame()
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return pd.DataFrame()
    
    def get_latest_data(self, symbol: str = "513330", days: int = 30) -> pd.DataFrame:
        """
        获取最近N天的数据
        
        Args:
            symbol: ETF代码
            days: 天数
            
        Returns:
            最近N天的数据
        """
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y%m%d")
        
        return self.fetch_etf_data(symbol, start_date, end_date)
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据完整性"""
        if data.empty:
            print("数据为空")
            return False
            
        required_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            print(f"缺少必要的列: {missing_columns}")
            return False
            
        # 检查是否有空值
        if data[required_columns].isnull().any().any():
            print("数据中存在空值")
            null_info = data[required_columns].isnull().sum()
            print("空值统计:")
            for col, count in null_info.items():
                if count > 0:
                    print(f"  {col}: {count} 个空值")
            return False
            
        print("数据验证通过")
        return True


def test_akshare_data():
    """测试akshare数据获取功能"""
    print("测试AKShare A股ETF数据获取")
    print("="*50)
    
    loader = AShareETFDataLoader()
    
    # 获取ETF基本信息
    print("1. 获取ETF基本信息:")
    etf_info = loader.get_etf_info("513330")
    for key, value in etf_info.items():
        print(f"   {key}: {value}")
    print()
    
    # 获取历史数据
    print("2. 获取历史数据:")
    data = loader.fetch_etf_data(
        symbol="513330",
        start_date="20240101",
        end_date=None  # 到今天
    )
    
    if not data.empty:
        print("数据预览:")
        print(data.head())
        print(f"\n数据形状: {data.shape}")
        print(f"数据范围: {data['date'].min()} 到 {data['date'].max()}")
        
        # 数据验证
        print("\n3. 数据验证:")
        is_valid = loader.validate_data(data)
        
        if is_valid:
            # 保存数据
            print("\n4. 保存数据:")
            loader.save_data(data)
            
            # 显示一些统计信息
            print("\n5. 数据统计:")
            print(f"   总交易日数: {len(data)}")
            print(f"   价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
            print(f"   平均成交量: {data['volume'].mean():,.0f}")
            print(f"   最大单日涨幅: {((data['close'] / data['close'].shift(1) - 1) * 100).max():.2f}%")
            print(f"   最大单日跌幅: {((data['close'] / data['close'].shift(1) - 1) * 100).min():.2f}%")
        
        return data
    else:
        print("数据获取失败")
        return pd.DataFrame()


if __name__ == "__main__":
    test_akshare_data()
