"""
数据获取模块 - 从Yahoo Finance获取ETF数据
"""

import yfinance as yf
import pandas as pd
from typing import List, Dict
from config import DATA_CONFIG, BACKTEST_CONFIG


class DataLoader:
    """ETF数据加载器"""
    
    def __init__(self):
        self.start_date = BACKTEST_CONFIG['start_date']
        self.end_date = BACKTEST_CONFIG['end_date']
        self.interval = DATA_CONFIG['interval']
    
    def fetch_etf_data(self, symbol: str) -> pd.DataFrame:
        """
        获取单个ETF的历史数据

        Args:
            symbol: ETF代码

        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            import time
            # 添加延迟以避免速率限制
            time.sleep(1)

            ticker = yf.Ticker(symbol)
            data = ticker.history(
                start=self.start_date,
                end=self.end_date,
                interval=self.interval
            )

            if data.empty:
                raise ValueError(f"无法获取 {symbol} 的数据")

            # 重命名列以符合pybroker的要求
            data = data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })

            # 添加symbol列
            data['symbol'] = symbol

            # 重置索引，将日期作为列
            data = data.reset_index()
            data = data.rename(columns={'Date': 'date'})

            print(f"成功获取 {symbol} 数据: {len(data)} 条记录")
            return data

        except Exception as e:
            print(f"获取 {symbol} 数据时出错: {e}")
            return pd.DataFrame()
    
    def fetch_multiple_etfs(self, symbols: List[str]) -> pd.DataFrame:
        """
        获取多个ETF的历史数据
        
        Args:
            symbols: ETF代码列表
            
        Returns:
            合并后的DataFrame
        """
        all_data = []
        
        for symbol in symbols:
            data = self.fetch_etf_data(symbol)
            if not data.empty:
                all_data.append(data)
        
        if all_data:
            combined_data = pd.concat(all_data, ignore_index=True)
            print(f"总共获取了 {len(combined_data)} 条数据记录")
            return combined_data
        else:
            raise ValueError("无法获取任何ETF数据")
    
    def save_data(self, data: pd.DataFrame, filename: str = 'etf_data.csv'):
        """保存数据到CSV文件"""
        data.to_csv(filename, index=False)
        print(f"数据已保存到 {filename}")
    
    def load_data(self, filename: str = 'etf_data.csv') -> pd.DataFrame:
        """从CSV文件加载数据"""
        try:
            data = pd.read_csv(filename)
            data['date'] = pd.to_datetime(data['date'])
            print(f"从 {filename} 加载了 {len(data)} 条数据记录")
            return data
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
            return pd.DataFrame()

    def generate_sample_data(self, symbol: str = 'SPY', days: int = 500) -> pd.DataFrame:
        """
        生成示例数据用于测试

        Args:
            symbol: ETF代码
            days: 生成的天数

        Returns:
            示例数据DataFrame
        """
        import numpy as np
        from datetime import datetime, timedelta

        # 生成日期序列
        start_date = datetime.strptime(self.start_date, '%Y-%m-%d')
        dates = [start_date + timedelta(days=i) for i in range(days)]

        # 生成价格数据 (模拟股价走势)
        np.random.seed(42)  # 固定随机种子以便重现

        # 初始价格
        initial_price = 300.0
        prices = [initial_price]

        # 生成价格序列 (随机游走 + 趋势)
        for i in range(1, days):
            # 添加一些趋势和随机波动
            trend = 0.0002  # 轻微上升趋势
            volatility = 0.02  # 波动率

            # 随机变化
            change = np.random.normal(trend, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))  # 确保价格不为负

        # 生成OHLC数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成开盘、最高、最低价
            daily_volatility = 0.01
            open_price = close * (1 + np.random.normal(0, daily_volatility/2))
            high_price = max(open_price, close) * (1 + abs(np.random.normal(0, daily_volatility/2)))
            low_price = min(open_price, close) * (1 - abs(np.random.normal(0, daily_volatility/2)))

            # 生成成交量
            volume = int(np.random.normal(50000000, 10000000))
            volume = max(volume, 1000000)  # 确保最小成交量

            data.append({
                'date': date,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close, 2),
                'volume': volume,
                'symbol': symbol
            })

        df = pd.DataFrame(data)
        print(f"生成了 {symbol} 的 {len(df)} 条示例数据")
        return df


if __name__ == "__main__":
    # 测试数据加载
    from config import ETF_SYMBOLS

    loader = DataLoader()

    # 首先尝试生成示例数据
    print("生成示例数据进行测试...")
    sample_data = []
    for symbol in ETF_SYMBOLS[:2]:  # 测试前两个ETF
        data = loader.generate_sample_data(symbol)
        sample_data.append(data)

    if sample_data:
        combined_data = pd.concat(sample_data, ignore_index=True)
        print("\n示例数据预览:")
        print(combined_data.head())
        print(f"\n数据形状: {combined_data.shape}")
        print(f"包含的ETF: {combined_data['symbol'].unique()}")

        # 保存示例数据
        loader.save_data(combined_data, 'sample_etf_data.csv')

    # 如果需要，也可以尝试获取真实数据（可能会遇到速率限制）
    print("\n如果需要真实数据，请稍后再试或使用VPN...")
