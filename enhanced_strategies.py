"""
增强版ETF交易策略集合
包含多种适合ETF操作的策略
"""

import pandas as pd
import numpy as np
from pybroker import ExecContext
from config import STRATEGY_CONFIG


def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, sma, lower_band


def calculate_macd(prices, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


# ==================== 策略1: 改进的双均线策略 ====================
def improved_ma_strategy(ctx):
    """
    改进的双均线策略
    
    改进点:
    1. 使用EMA代替SMA，响应更快
    2. 添加成交量确认
    3. 添加止损机制
    4. 分批建仓
    """
    
    if ctx.bars < 60:
        return
    
    close_prices = pd.Series(ctx.close)
    volume = pd.Series(ctx.volume)
    
    # 使用EMA代替SMA
    ema_short = close_prices.ewm(span=20).mean()
    ema_long = close_prices.ewm(span=50).mean()
    
    # 成交量均线
    vol_ma = volume.rolling(window=20).mean()
    
    current_short = ema_short.iloc[-1]
    current_long = ema_long.iloc[-1]
    prev_short = ema_short.iloc[-2]
    prev_long = ema_long.iloc[-2]
    current_vol = volume.iloc[-1]
    avg_vol = vol_ma.iloc[-1]
    
    # 金叉信号 + 成交量确认
    golden_cross = (prev_short <= prev_long) and (current_short > current_long)
    volume_confirm = current_vol > avg_vol * 1.2  # 成交量放大20%
    
    # 死叉信号
    death_cross = (prev_short >= prev_long) and (current_short < current_long)
    
    # 止损条件：价格跌破20日EMA的5%
    stop_loss = ctx.long_pos() and close_prices.iloc[-1] < ema_short.iloc[-1] * 0.95
    
    if golden_cross and volume_confirm and not ctx.long_pos():
        # 分批建仓：先买入50%
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        print(f"{ctx.date} {ctx.symbol}: 改进均线策略-金叉买入50%仓位")
    
    elif death_cross and ctx.long_pos():
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: 改进均线策略-死叉卖出")
    
    elif stop_loss:
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: 改进均线策略-止损卖出")


# ==================== 策略2: RSI均值回归策略 ====================
def rsi_mean_reversion_strategy(ctx):
    """
    RSI均值回归策略
    
    适合ETF的震荡行情:
    - RSI < 30 时买入（超卖）
    - RSI > 70 时卖出（超买）
    - 适合指数ETF的震荡操作
    """
    
    if ctx.bars < 30:
        return
    
    close_prices = pd.Series(ctx.close)
    rsi = calculate_rsi(close_prices, period=14)
    
    current_rsi = rsi.iloc[-1]
    prev_rsi = rsi.iloc[-2]
    
    if pd.isna(current_rsi) or pd.isna(prev_rsi):
        return
    
    # 超卖买入信号
    oversold_buy = (prev_rsi <= 30) and (current_rsi > 30) and not ctx.long_pos()
    
    # 超买卖出信号
    overbought_sell = (prev_rsi >= 70) and (current_rsi < 70) and ctx.long_pos()
    
    # 极度超卖加仓信号
    extreme_oversold = current_rsi < 20 and ctx.long_pos()
    
    if oversold_buy:
        ctx.buy_shares = ctx.calc_target_shares(0.8)
        print(f"{ctx.date} {ctx.symbol}: RSI策略-超卖买入, RSI={current_rsi:.1f}")
    
    elif overbought_sell:
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: RSI策略-超买卖出, RSI={current_rsi:.1f}")
    
    elif extreme_oversold and ctx.cash > 1000:
        # 极度超卖时加仓
        additional_shares = min(ctx.calc_target_shares(0.2), ctx.cash // close_prices.iloc[-1])
        if additional_shares > 0:
            ctx.buy_shares = additional_shares
            print(f"{ctx.date} {ctx.symbol}: RSI策略-极度超卖加仓, RSI={current_rsi:.1f}")


# ==================== 策略3: 布林带突破策略 ====================
def bollinger_breakout_strategy(ctx):
    """
    布林带突破策略
    
    策略逻辑:
    - 价格突破上轨时买入（突破信号）
    - 价格跌破下轨时卖出（止损）
    - 价格回到中轨附近时部分获利了结
    """
    
    if ctx.bars < 25:
        return
    
    close_prices = pd.Series(ctx.close)
    upper_band, middle_band, lower_band = calculate_bollinger_bands(close_prices, period=20, std_dev=2)
    
    current_price = close_prices.iloc[-1]
    prev_price = close_prices.iloc[-2]
    current_upper = upper_band.iloc[-1]
    current_lower = lower_band.iloc[-1]
    current_middle = middle_band.iloc[-1]
    prev_upper = upper_band.iloc[-2]
    prev_lower = lower_band.iloc[-2]
    
    if pd.isna(current_upper) or pd.isna(current_lower):
        return
    
    # 突破上轨买入
    breakout_buy = (prev_price <= prev_upper) and (current_price > current_upper) and not ctx.long_pos()
    
    # 跌破下轨卖出
    breakdown_sell = (prev_price >= prev_lower) and (current_price < current_lower) and ctx.long_pos()
    
    # 回到中轨附近部分获利
    profit_taking = ctx.long_pos() and current_price < current_middle and prev_price >= current_middle
    
    if breakout_buy:
        ctx.buy_shares = ctx.calc_target_shares(0.9)
        print(f"{ctx.date} {ctx.symbol}: 布林带策略-突破买入")
    
    elif breakdown_sell:
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: 布林带策略-跌破下轨卖出")
    
    elif profit_taking:
        # 卖出一半仓位获利
        sell_shares = ctx.long_pos_size // 2
        if sell_shares > 0:
            ctx.sell_shares = sell_shares
            print(f"{ctx.date} {ctx.symbol}: 布林带策略-回归中轨部分获利")


# ==================== 策略4: MACD趋势策略 ====================
def macd_trend_strategy(ctx):
    """
    MACD趋势策略
    
    策略逻辑:
    - MACD金叉且在零轴上方时买入
    - MACD死叉或跌破零轴时卖出
    - 适合捕捉中期趋势
    """
    
    if ctx.bars < 35:
        return
    
    close_prices = pd.Series(ctx.close)
    macd_line, signal_line, histogram = calculate_macd(close_prices)
    
    current_macd = macd_line.iloc[-1]
    current_signal = signal_line.iloc[-1]
    prev_macd = macd_line.iloc[-2]
    prev_signal = signal_line.iloc[-2]
    
    if pd.isna(current_macd) or pd.isna(current_signal):
        return
    
    # MACD金叉
    macd_golden_cross = (prev_macd <= prev_signal) and (current_macd > current_signal)
    
    # MACD死叉
    macd_death_cross = (prev_macd >= prev_signal) and (current_macd < current_signal)
    
    # 强势信号：MACD在零轴上方
    strong_trend = current_macd > 0
    
    if macd_golden_cross and strong_trend and not ctx.long_pos():
        ctx.buy_shares = ctx.calc_target_shares(0.9)
        print(f"{ctx.date} {ctx.symbol}: MACD策略-金叉买入")
    
    elif macd_death_cross and ctx.long_pos():
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: MACD策略-死叉卖出")
    
    elif ctx.long_pos() and current_macd < 0:
        # MACD跌破零轴，趋势转弱
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: MACD策略-跌破零轴卖出")


# ==================== 策略5: 多因子综合策略 ====================
def multi_factor_strategy(ctx):
    """
    多因子综合策略
    
    综合考虑:
    - 趋势因子（均线）
    - 动量因子（RSI）
    - 波动因子（布林带）
    - 成交量因子
    """
    
    if ctx.bars < 60:
        return
    
    close_prices = pd.Series(ctx.close)
    volume = pd.Series(ctx.volume)
    
    # 计算各种指标
    ema_20 = close_prices.ewm(span=20).mean()
    ema_50 = close_prices.ewm(span=50).mean()
    rsi = calculate_rsi(close_prices, period=14)
    upper_band, middle_band, lower_band = calculate_bollinger_bands(close_prices)
    vol_ma = volume.rolling(window=20).mean()
    
    current_price = close_prices.iloc[-1]
    current_rsi = rsi.iloc[-1]
    current_vol = volume.iloc[-1]
    avg_vol = vol_ma.iloc[-1]
    
    if pd.isna(current_rsi):
        return
    
    # 多因子评分系统
    score = 0
    
    # 趋势因子 (权重: 30%)
    if ema_20.iloc[-1] > ema_50.iloc[-1]:
        score += 30
    
    # 动量因子 (权重: 25%)
    if 30 < current_rsi < 70:  # RSI在合理区间
        score += 25
    elif current_rsi < 30:  # 超卖
        score += 15
    
    # 波动因子 (权重: 25%)
    if current_price > middle_band.iloc[-1]:  # 价格在中轨上方
        score += 25
    elif current_price < lower_band.iloc[-1]:  # 价格在下轨下方（超卖）
        score += 15
    
    # 成交量因子 (权重: 20%)
    if current_vol > avg_vol * 1.1:  # 成交量放大
        score += 20
    
    # 根据评分决定操作
    if score >= 70 and not ctx.long_pos():
        # 高分买入
        position_size = min(0.9, score / 100)
        ctx.buy_shares = ctx.calc_target_shares(position_size)
        print(f"{ctx.date} {ctx.symbol}: 多因子策略-买入, 评分={score}")
    
    elif score <= 30 and ctx.long_pos():
        # 低分卖出
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: 多因子策略-卖出, 评分={score}")


# 策略映射字典
STRATEGY_MAP = {
    'improved_ma': improved_ma_strategy,
    'rsi_mean_reversion': rsi_mean_reversion_strategy,
    'bollinger_breakout': bollinger_breakout_strategy,
    'macd_trend': macd_trend_strategy,
    'multi_factor': multi_factor_strategy,
}


def get_strategy_list():
    """获取所有可用策略列表"""
    return {
        'improved_ma': '改进双均线策略',
        'rsi_mean_reversion': 'RSI均值回归策略',
        'bollinger_breakout': '布林带突破策略',
        'macd_trend': 'MACD趋势策略',
        'multi_factor': '多因子综合策略',
    }
