"""
策略优化测试框架
包含参数优化、策略组合、风险管理等高级功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pybroker
from pybroker import Strategy, StrategyConfig
from pybroker.common import FeeMode
from itertools import product
import warnings
warnings.filterwarnings('ignore')

from enhanced_strategies import STRATEGY_MAP
from advanced_strategies import adaptive_multi_factor_strategy
from ml_enhanced_strategies import ml_enhanced_strategy, ensemble_meta_strategy
from config import BACKTEST_CONFIG

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class StrategyOptimizer:
    """策略优化器"""
    
    def __init__(self, data_file='ashare_etf_backtest_data.csv'):
        self.data_file = data_file
        self.data = None
        self.optimization_results = {}
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file, encoding='utf-8-sig')
            self.data['date'] = pd.to_datetime(self.data['date'])
            print(f"成功加载数据: {len(self.data)} 条记录")
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def optimize_parameters(self, strategy_name, symbol='159915', param_ranges=None):
        """参数优化"""
        if self.data is None:
            print("请先加载数据")
            return
        
        # 默认参数范围
        if param_ranges is None:
            param_ranges = {
                'ma_short': [10, 15, 20, 25, 30],
                'ma_long': [40, 50, 60, 70, 80],
                'rsi_oversold': [20, 25, 30, 35],
                'rsi_overbought': [65, 70, 75, 80],
                'stop_loss': [0.92, 0.94, 0.95, 0.96, 0.98],
                'take_profit': [1.05, 1.08, 1.10, 1.12, 1.15]
            }
        
        # 筛选数据
        symbol_data = self.data[self.data['symbol'] == symbol].copy()
        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
        
        print(f"开始优化 {strategy_name} 策略参数...")
        print(f"数据范围: {symbol_data['date'].min().date()} 到 {symbol_data['date'].max().date()}")
        
        # 生成参数组合
        param_combinations = list(product(*param_ranges.values()))
        param_names = list(param_ranges.keys())
        
        results = []
        
        for i, params in enumerate(param_combinations[:50]):  # 限制测试数量
            if i % 10 == 0:
                print(f"进度: {i}/{min(50, len(param_combinations))}")
            
            param_dict = dict(zip(param_names, params))
            
            try:
                # 运行回测
                result = self.run_single_backtest(
                    strategy_name, symbol_data, param_dict
                )
                
                if result:
                    results.append({
                        'params': param_dict,
                        'total_return': result.get('total_return', 0),
                        'max_drawdown': result.get('max_drawdown', 0),
                        'sharpe_ratio': result.get('sharpe_ratio', 0),
                        'trade_count': result.get('trade_count', 0),
                        'win_rate': result.get('win_rate', 0)
                    })
            except Exception as e:
                continue
        
        if results:
            # 排序结果
            results_df = pd.DataFrame(results)
            
            # 多目标优化评分
            results_df['score'] = (
                results_df['total_return'] * 0.4 +
                (100 + results_df['max_drawdown']) * 0.3 +  # 回撤越小越好
                results_df['sharpe_ratio'] * 0.2 +
                results_df['win_rate'] * 0.1
            )
            
            results_df = results_df.sort_values('score', ascending=False)
            
            self.optimization_results[f"{strategy_name}_{symbol}"] = results_df
            
            print(f"\n{strategy_name} 参数优化完成!")
            print("最佳参数组合:")
            best_params = results_df.iloc[0]['params']
            for param, value in best_params.items():
                print(f"  {param}: {value}")
            
            print(f"最佳表现:")
            print(f"  总收益率: {results_df.iloc[0]['total_return']:.2f}%")
            print(f"  最大回撤: {results_df.iloc[0]['max_drawdown']:.2f}%")
            print(f"  夏普比率: {results_df.iloc[0]['sharpe_ratio']:.2f}")
            print(f"  胜率: {results_df.iloc[0]['win_rate']:.1f}%")
            
            return results_df
        else:
            print("参数优化失败，未获得有效结果")
            return None
    
    def run_single_backtest(self, strategy_name, data, params):
        """运行单次回测"""
        try:
            from ashare_etf_backtest_final import AShareETFDataSource
            
            # 创建数据源
            data_source = AShareETFDataSource(data)
            
            # 创建策略配置
            config = StrategyConfig(
                initial_cash=BACKTEST_CONFIG['initial_cash'],
                fee_mode=FeeMode.ORDER_PERCENT,
                fee_amount=BACKTEST_CONFIG['commission']
            )
            
            # 创建策略
            strategy = Strategy(
                data_source=data_source,
                start_date=data['date'].min().strftime('%Y-%m-%d'),
                end_date=data['date'].max().strftime('%Y-%m-%d'),
                config=config
            )
            
            # 选择策略函数
            if strategy_name in STRATEGY_MAP:
                strategy_func = STRATEGY_MAP[strategy_name]
            elif strategy_name == 'adaptive_multi_factor':
                strategy_func = adaptive_multi_factor_strategy
            elif strategy_name == 'ml_enhanced':
                strategy_func = ml_enhanced_strategy
            elif strategy_name == 'ensemble_meta':
                strategy_func = ensemble_meta_strategy
            else:
                return None
            
            # 添加策略函数
            symbol = data['symbol'].iloc[0]
            strategy.add_execution(strategy_func, symbols=[symbol])
            
            # 运行回测
            result = strategy.backtest()
            
            if result and hasattr(result, 'metrics_df'):
                metrics = result.metrics_df.set_index('name')['value']
                
                return {
                    'total_return': metrics.get('total_return_pct', 0),
                    'max_drawdown': metrics.get('max_drawdown_pct', 0),
                    'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                    'trade_count': int(metrics.get('trade_count', 0)),
                    'win_rate': metrics.get('win_rate', 0) * 100 if 'win_rate' in metrics else 0
                }
            
        except Exception as e:
            return None
        
        return None
    
    def compare_advanced_strategies(self, symbol='159915'):
        """对比高级策略"""
        if self.data is None:
            print("请先加载数据")
            return
        
        # 筛选数据
        symbol_data = self.data[self.data['symbol'] == symbol].copy()
        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
        
        print(f"开始对比高级策略 - {symbol}")
        
        # 测试策略列表
        advanced_strategies = {
            'adaptive_multi_factor': '自适应多因子策略',
            'ml_enhanced': '机器学习增强策略',
            'ensemble_meta': '集成元策略'
        }
        
        results = {}
        
        for strategy_key, strategy_name in advanced_strategies.items():
            print(f"\n测试 {strategy_name}...")
            
            try:
                result = self.run_single_backtest(strategy_key, symbol_data, {})
                if result:
                    results[strategy_key] = result
                    print(f"{strategy_name} 测试完成")
                else:
                    print(f"{strategy_name} 测试失败")
            except Exception as e:
                print(f"{strategy_name} 测试出错: {e}")
        
        # 显示对比结果
        if results:
            self.print_advanced_comparison(results, advanced_strategies)
            return results
        else:
            print("所有高级策略测试失败")
            return None
    
    def print_advanced_comparison(self, results, strategy_names):
        """打印高级策略对比结果"""
        print("\n" + "="*80)
        print("高级策略对比结果")
        print("="*80)
        
        print(f"{'策略名称':<20} {'总收益率':<12} {'最大回撤':<12} {'夏普比率':<12} {'交易次数':<8} {'胜率':<8}")
        print("-" * 80)
        
        # 按总收益率排序
        sorted_results = sorted(results.items(), key=lambda x: x[1]['total_return'], reverse=True)
        
        for i, (strategy_key, result) in enumerate(sorted_results):
            strategy_name = strategy_names.get(strategy_key, strategy_key)[:18]
            rank_symbol = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "  "
            
            print(f"{rank_symbol}{strategy_name:<18} {result['total_return']:<12.2f}% "
                  f"{result['max_drawdown']:<12.2f}% {result['sharpe_ratio']:<12.2f} "
                  f"{result['trade_count']:<8} {result['win_rate']:<8.1f}%")
        
        print("-" * 80)
        
        # 策略特点分析
        best_strategy = sorted_results[0]
        print(f"\n🏆 最佳策略: {strategy_names[best_strategy[0]]}")
        print(f"📈 总收益率: {best_strategy[1]['total_return']:.2f}%")
        print(f"🛡️ 最大回撤: {best_strategy[1]['max_drawdown']:.2f}%")
        print(f"⚡ 交易次数: {best_strategy[1]['trade_count']}")
    
    def portfolio_optimization(self, strategies, symbols=['159915', '159920']):
        """投资组合优化"""
        print("开始投资组合优化...")
        
        portfolio_results = {}
        
        # 测试不同权重组合
        weight_combinations = [
            [1.0, 0.0],  # 只投资第一个ETF
            [0.0, 1.0],  # 只投资第二个ETF
            [0.7, 0.3],  # 70%-30%
            [0.6, 0.4],  # 60%-40%
            [0.5, 0.5],  # 50%-50%
            [0.4, 0.6],  # 40%-60%
            [0.3, 0.7],  # 30%-70%
        ]
        
        for weights in weight_combinations:
            portfolio_name = f"{weights[0]:.1f}-{weights[1]:.1f}"
            print(f"测试投资组合权重: {portfolio_name}")
            
            # 这里可以实现投资组合回测逻辑
            # 简化版本：假设组合收益是加权平均
            portfolio_results[portfolio_name] = {
                'weights': weights,
                'total_return': np.random.uniform(-10, 15),  # 模拟结果
                'max_drawdown': np.random.uniform(-25, -5),
                'sharpe_ratio': np.random.uniform(0, 1.5)
            }
        
        return portfolio_results
    
    def plot_optimization_results(self, strategy_name):
        """绘制优化结果"""
        key = f"{strategy_name}_159915"
        if key not in self.optimization_results:
            print("没有找到优化结果")
            return
        
        results_df = self.optimization_results[key]
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 收益率分布
        ax1.hist(results_df['total_return'], bins=20, alpha=0.7, color='blue')
        ax1.set_title('总收益率分布')
        ax1.set_xlabel('总收益率 (%)')
        ax1.set_ylabel('频次')
        ax1.grid(True, alpha=0.3)
        
        # 回撤分布
        ax2.hist(results_df['max_drawdown'], bins=20, alpha=0.7, color='red')
        ax2.set_title('最大回撤分布')
        ax2.set_xlabel('最大回撤 (%)')
        ax2.set_ylabel('频次')
        ax2.grid(True, alpha=0.3)
        
        # 收益率vs回撤散点图
        ax3.scatter(results_df['max_drawdown'], results_df['total_return'], alpha=0.6)
        ax3.set_xlabel('最大回撤 (%)')
        ax3.set_ylabel('总收益率 (%)')
        ax3.set_title('收益率 vs 回撤')
        ax3.grid(True, alpha=0.3)
        
        # 夏普比率分布
        ax4.hist(results_df['sharpe_ratio'], bins=20, alpha=0.7, color='green')
        ax4.set_title('夏普比率分布')
        ax4.set_xlabel('夏普比率')
        ax4.set_ylabel('频次')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()


def main():
    """主函数"""
    print("策略优化测试系统")
    print("="*50)
    
    # 创建优化器
    optimizer = StrategyOptimizer()
    
    if optimizer.data is None:
        print("数据加载失败，退出程序")
        return
    
    # 1. 对比高级策略
    print("\n1. 高级策略对比测试")
    advanced_results = optimizer.compare_advanced_strategies(symbol='159915')
    
    # 2. 参数优化示例
    print("\n2. 参数优化示例")
    param_ranges = {
        'ma_short': [15, 20, 25],
        'ma_long': [50, 60, 70],
        'rsi_oversold': [25, 30, 35],
        'rsi_overbought': [65, 70, 75]
    }
    
    # 优化多因子策略参数
    optimization_results = optimizer.optimize_parameters(
        'multi_factor', 
        symbol='159915', 
        param_ranges=param_ranges
    )
    
    # 3. 投资组合优化
    print("\n3. 投资组合优化")
    portfolio_results = optimizer.portfolio_optimization(['multi_factor'])
    
    print("\n优化测试完成!")


if __name__ == "__main__":
    main()
