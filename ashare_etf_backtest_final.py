"""
A股ETF均线策略回测 - 使用实际可获取的ETF数据
包括创业板ETF(159915)和恒生ETF(159920)等
"""

import akshare as ak
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pybroker
from pybroker import Strategy, StrategyConfig
from pybroker.data import DataSource
from datetime import datetime, timedelta

from strategy import ma_cross_strategy, get_strategy_info
from config import STRATEGY_CONFIG, BACKTEST_CONFIG

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class AShareETFDataSource(DataSource):
    """A股ETF数据源"""
    
    def __init__(self, df):
        super().__init__()
        self.df = df.copy()
        if 'date' in self.df.columns:
            self.df['date'] = pd.to_datetime(self.df['date'])
    
    def _fetch_data(self, symbols, start_date, end_date, **kwargs):
        return self.query(symbols, start_date, end_date, **kwargs)
    
    def query(self, symbols, start_date=None, end_date=None, timeframe=None, adjust=None, **kwargs):
        if start_date is None:
            start_date = self.df['date'].min()
        if end_date is None:
            end_date = self.df['date'].max()
            
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        filtered_df = self.df[
            (self.df['symbol'].isin(symbols)) &
            (self.df['date'] >= start_date) &
            (self.df['date'] <= end_date)
        ].copy()
        
        return filtered_df


class AShareETFBacktester:
    """A股ETF回测器"""
    
    def __init__(self):
        # 可成功获取数据的ETF列表
        self.available_etfs = {
            '159915': '创业板ETF',
            '159919': '300ETF', 
            '159949': '创业板50',
            '159995': '芯片ETF',
            '159928': '消费ETF',
            '159941': '纳指ETF',
            '159920': '恒生ETF'
        }
        self.data = None
        self.results = {}
        
    def fetch_etf_data(self, symbol: str, start_date: str = "20240101") -> pd.DataFrame:
        """获取单个ETF数据"""
        end_date = datetime.now().strftime("%Y%m%d")
        
        try:
            print(f"正在获取 {symbol}({self.available_etfs.get(symbol, 'Unknown')}) 的数据...")
            
            data = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=""
            )
            
            if data.empty:
                print(f"未获取到 {symbol} 的数据")
                return pd.DataFrame()
            
            # 数据清理
            data_clean = data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume'
            })
            
            data_clean['date'] = pd.to_datetime(data_clean['date'])
            data_clean['symbol'] = symbol
            
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']
            data_clean = data_clean[required_columns]
            data_clean = data_clean.sort_values('date').reset_index(drop=True)
            
            print(f"成功获取 {symbol} 数据: {len(data_clean)} 条记录")
            print(f"数据范围: {data_clean['date'].min().date()} 到 {data_clean['date'].max().date()}")
            
            return data_clean
            
        except Exception as e:
            print(f"获取 {symbol} 数据时出错: {e}")
            return pd.DataFrame()
    
    def load_multiple_etfs(self, symbols: list = None, start_date: str = "20240101") -> pd.DataFrame:
        """加载多个ETF数据"""
        if symbols is None:
            # 默认选择几个代表性的ETF
            symbols = ['159915', '159920']  # 创业板ETF和恒生ETF
            
        print(f"准备获取ETF数据: {symbols}")
        all_data = []
        
        for symbol in symbols:
            data = self.fetch_etf_data(symbol, start_date)
            if not data.empty:
                all_data.append(data)
                # 添加延迟
                import time
                time.sleep(1)
        
        if all_data:
            combined_data = pd.concat(all_data, ignore_index=True)
            print(f"\n总共获取了 {len(combined_data)} 条数据记录")
            print(f"包含的ETF: {list(combined_data['symbol'].unique())}")
            self.data = combined_data
            return combined_data
        else:
            print("未获取到任何ETF数据")
            return pd.DataFrame()
    
    def run_backtest(self, symbol: str):
        """运行单个ETF的回测"""
        print(f"\n开始回测 {symbol}({self.available_etfs.get(symbol, 'Unknown')})...")
        
        symbol_data = self.data[self.data['symbol'] == symbol].copy()
        
        if symbol_data.empty:
            print(f"没有找到 {symbol} 的数据")
            return None
        
        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
        
        print(f"{symbol} 数据范围: {symbol_data['date'].min().date()} 到 {symbol_data['date'].max().date()}")
        print(f"数据点数: {len(symbol_data)}")
        
        # 显示一些基本统计
        price_range = f"{symbol_data['close'].min():.3f} - {symbol_data['close'].max():.3f}"
        print(f"价格范围: {price_range}")
        
        try:
            # 启用缓存
            pybroker.enable_data_source_cache(f'backtest_{symbol}')
            
            # 创建策略配置
            from pybroker.common import FeeMode
            config = StrategyConfig(
                initial_cash=BACKTEST_CONFIG['initial_cash'],
                fee_mode=FeeMode.ORDER_PERCENT,
                fee_amount=BACKTEST_CONFIG['commission']
            )
            
            # 创建数据源
            data_source = AShareETFDataSource(symbol_data)
            
            # 创建策略
            strategy = Strategy(
                data_source=data_source,
                start_date=symbol_data['date'].min().strftime('%Y-%m-%d'),
                end_date=symbol_data['date'].max().strftime('%Y-%m-%d'),
                config=config
            )
            
            # 添加策略函数
            strategy.add_execution(ma_cross_strategy, symbols=[symbol])
            
            # 运行回测
            result = strategy.backtest()
            
            if result:
                print(f"{symbol} 回测完成")
                self.results[symbol] = result
                return result
            else:
                print(f"{symbol} 回测失败")
                return None
                
        except Exception as e:
            print(f"{symbol} 回测出错: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_all_backtests(self):
        """运行所有ETF的回测"""
        if self.data is None or self.data.empty:
            print("请先加载数据")
            return
        
        symbols = self.data['symbol'].unique()
        print(f"将对以下ETF进行回测: {list(symbols)}")
        
        for symbol in symbols:
            self.run_backtest(symbol)
    
    def print_results(self):
        """打印回测结果"""
        if not self.results:
            print("没有回测结果")
            return
        
        print("\n" + "="*80)
        print("A股ETF均线策略回测结果汇总")
        print("="*80)
        
        strategy_info = get_strategy_info()
        print(f"策略: {strategy_info['name']}")
        print(f"描述: {strategy_info['description']}")
        print(f"回测期间: 2024年1月1日 到 现在")
        print(f"初始资金: ¥{BACKTEST_CONFIG['initial_cash']:,.2f}")
        print(f"手续费率: {BACKTEST_CONFIG['commission']*100:.2f}%")
        
        print("\n各ETF表现:")
        print("-" * 100)
        print(f"{'ETF代码':<8} {'ETF名称':<15} {'总收益率':<12} {'最大回撤':<12} {'交易次数':<8} {'胜率':<8}")
        print("-" * 100)
        
        for symbol, result in self.results.items():
            etf_name = self.available_etfs.get(symbol, 'Unknown')[:13]
            
            if result and hasattr(result, 'metrics_df'):
                metrics = result.metrics_df.set_index('name')['value']
                
                total_return = metrics.get('total_return_pct', 0)
                max_drawdown = metrics.get('max_drawdown_pct', 0)
                trades = int(metrics.get('trade_count', 0))
                win_rate = metrics.get('win_rate', 0) * 100 if 'win_rate' in metrics else 0
                
                print(f"{symbol:<8} {etf_name:<15} {total_return:<12.2f}% {max_drawdown:<12.2f}% {trades:<8} {win_rate:<8.1f}%")
            else:
                print(f"{symbol:<8} {etf_name:<15} {'N/A':<12} {'N/A':<12} {'N/A':<8} {'N/A':<8}")
        
        print("-" * 100)
    
    def plot_results(self):
        """绘制回测结果"""
        if not self.results:
            print("没有回测结果可绘制")
            return
        
        symbols = list(self.results.keys())
        fig, axes = plt.subplots(len(symbols), 1, figsize=(12, 6*len(symbols)))
        if len(symbols) == 1:
            axes = [axes]
        
        for i, symbol in enumerate(symbols):
            result = self.results[symbol]
            etf_name = self.available_etfs.get(symbol, 'Unknown')
            
            if result and hasattr(result, 'portfolio'):
                portfolio = result.portfolio
                
                axes[i].plot(portfolio.index, portfolio['market_value'], 
                           label='组合净值', linewidth=2, color='blue')
                axes[i].set_title(f'{symbol}({etf_name}) - 策略表现')
                axes[i].set_xlabel('日期')
                axes[i].set_ylabel('组合价值 (¥)')
                axes[i].legend()
                axes[i].grid(True, alpha=0.3)
                
                # 添加收益率信息
                if hasattr(result, 'metrics_df'):
                    metrics = result.metrics_df.set_index('name')['value']
                    total_return = metrics.get('total_return_pct', 0)
                    max_drawdown = metrics.get('max_drawdown_pct', 0)
                    axes[i].text(0.02, 0.98, 
                               f'总收益率: {total_return:.2f}%\n最大回撤: {max_drawdown:.2f}%',
                               transform=axes[i].transAxes, 
                               verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.show()
    
    def save_data(self, filename: str = 'ashare_etf_backtest_data.csv'):
        """保存数据"""
        if self.data is not None and not self.data.empty:
            self.data.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"数据已保存到 {filename}")


def main():
    """主函数"""
    print("A股ETF均线策略回测系统")
    print("="*60)
    
    # 显示策略信息
    strategy_info = get_strategy_info()
    print("策略配置:")
    for key, value in strategy_info.items():
        print(f"  {key}: {value}")
    print()
    
    # 创建回测器
    backtester = AShareETFBacktester()
    
    # 显示可用的ETF
    print("可用的A股ETF:")
    for code, name in backtester.available_etfs.items():
        print(f"  {code}: {name}")
    print()
    
    # 选择要测试的ETF
    test_symbols = ['159915', '159920']  # 创业板ETF和恒生ETF
    print(f"本次测试ETF: {test_symbols}")
    
    # 加载数据
    data = backtester.load_multiple_etfs(test_symbols, start_date="20240101")
    
    if data.empty:
        print("数据加载失败，退出程序")
        return
    
    # 保存数据
    backtester.save_data()
    
    # 运行回测
    try:
        backtester.run_all_backtests()
    except Exception as e:
        print(f"回测运行失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 显示结果
    backtester.print_results()
    
    # 绘制图表
    try:
        backtester.plot_results()
    except Exception as e:
        print(f"绘图失败: {e}")


if __name__ == "__main__":
    main()
