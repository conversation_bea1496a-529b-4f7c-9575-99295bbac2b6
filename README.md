# ETF均线策略回测系统

基于PyBroker框架实现的ETF均线交叉策略回测系统。

## 策略说明

本项目实现了一个简单但经典的技术分析策略：

- **买入信号**: 当30日移动平均线上穿60日移动平均线时（金叉）
- **卖出信号**: 当30日移动平均线下穿60日移动平均线时（死叉）
- **标的**: 主要美股ETF（SPY、QQQ、IWM、VTI等）

## 项目结构

```
pybroker-test/
├── main.py              # 主程序，执行回测
├── strategy.py          # 策略逻辑实现
├── data_loader.py       # 数据获取模块
├── config.py            # 配置参数
├── requirements.txt     # 项目依赖
└── README.md           # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基本使用

直接运行主程序：

```bash
python main.py
```

### 2. 测试数据加载

```bash
python data_loader.py
```

### 3. 查看策略信息

```bash
python strategy.py
```

## 配置说明

在 `config.py` 中可以调整以下参数：

### ETF标的
```python
ETF_SYMBOLS = [
    'SPY',   # SPDR S&P 500 ETF
    'QQQ',   # Invesco QQQ Trust
    'IWM',   # iShares Russell 2000 ETF
    'VTI',   # Vanguard Total Stock Market ETF
]
```

### 回测参数
```python
BACKTEST_CONFIG = {
    'start_date': '2020-01-01',  # 回测开始日期
    'end_date': '2024-01-01',    # 回测结束日期
    'initial_cash': 100000,      # 初始资金
    'commission': 0.001,         # 手续费率
}
```

### 策略参数
```python
STRATEGY_CONFIG = {
    'short_ma_period': 30,       # 短期均线周期
    'long_ma_period': 60,        # 长期均线周期
    'position_size': 0.95,       # 仓位大小
}
```

## 功能特性

1. **数据获取**: 自动从Yahoo Finance获取ETF历史数据
2. **策略回测**: 基于PyBroker框架的专业回测引擎
3. **结果分析**: 详细的回测统计指标
4. **可视化**: 策略表现图表展示
5. **多标的**: 支持同时回测多个ETF

## 回测指标

系统会输出以下关键指标：

- 总收益率
- 年化收益率
- 最大回撤
- 夏普比率
- 交易次数
- 胜率等

## 注意事项

1. 首次运行会从网络下载数据，可能需要一些时间
2. 数据会自动保存到本地CSV文件，避免重复下载
3. 策略仅供学习研究，实际投资请谨慎
4. 回测结果不代表未来表现

## 扩展功能

可以考虑添加的功能：

- 更多技术指标（RSI、MACD等）
- 止损止盈机制
- 仓位管理优化
- 多时间框架分析
- 风险管理模块

## 依赖说明

- `pybroker`: 专业的量化回测框架
- `yfinance`: Yahoo Finance数据接口
- `pandas`: 数据处理
- `numpy`: 数值计算
- `matplotlib`: 图表绘制
