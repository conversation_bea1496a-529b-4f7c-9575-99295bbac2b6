"""
高级ETF交易策略优化版本
包含机器学习、动态参数调整、市场状态识别等高级功能
"""

import pandas as pd
import numpy as np
from pybroker import ExecContext
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')


class MarketRegimeDetector:
    """市场状态检测器"""
    
    def __init__(self, lookback=20):
        self.lookback = lookback
        self.scaler = StandardScaler()
        self.model = RandomForestClassifier(n_estimators=50, random_state=42)
        self.is_trained = False
    
    def calculate_features(self, prices, volumes):
        """计算市场特征"""
        if len(prices) < self.lookback:
            return None
        
        # 价格特征
        returns = prices.pct_change().dropna()
        volatility = returns.rolling(10).std().iloc[-1] if len(returns) >= 10 else 0
        trend_strength = abs(prices.iloc[-1] / prices.iloc[-self.lookback] - 1)
        
        # 成交量特征
        vol_ratio = volumes.iloc[-1] / volumes.rolling(10).mean().iloc[-1] if len(volumes) >= 10 else 1
        
        # 技术指标特征
        sma_5 = prices.rolling(5).mean().iloc[-1] if len(prices) >= 5 else prices.iloc[-1]
        sma_20 = prices.rolling(20).mean().iloc[-1] if len(prices) >= 20 else prices.iloc[-1]
        price_position = (prices.iloc[-1] - sma_20) / sma_20 if sma_20 != 0 else 0
        
        return np.array([volatility, trend_strength, vol_ratio, price_position]).reshape(1, -1)
    
    def detect_regime(self, prices, volumes):
        """检测市场状态：0=震荡, 1=上涨趋势, 2=下跌趋势"""
        features = self.calculate_features(prices, volumes)
        if features is None or not self.is_trained:
            return 0  # 默认震荡状态
        
        try:
            features_scaled = self.scaler.transform(features)
            regime = self.model.predict(features_scaled)[0]
            return regime
        except:
            return 0
    
    def train_model(self, historical_data):
        """训练市场状态识别模型"""
        if len(historical_data) < 100:
            return
        
        features_list = []
        labels_list = []
        
        for i in range(self.lookback, len(historical_data) - 10):
            prices = historical_data['close'].iloc[i-self.lookback:i]
            volumes = historical_data['volume'].iloc[i-self.lookback:i]
            
            features = self.calculate_features(prices, volumes)
            if features is not None:
                # 计算未来10天的标签
                future_return = (historical_data['close'].iloc[i+10] / historical_data['close'].iloc[i] - 1)
                if future_return > 0.02:
                    label = 1  # 上涨趋势
                elif future_return < -0.02:
                    label = 2  # 下跌趋势
                else:
                    label = 0  # 震荡
                
                features_list.append(features.flatten())
                labels_list.append(label)
        
        if len(features_list) > 50:
            X = np.array(features_list)
            y = np.array(labels_list)
            
            self.scaler.fit(X)
            X_scaled = self.scaler.transform(X)
            self.model.fit(X_scaled, y)
            self.is_trained = True


class DynamicParameterOptimizer:
    """动态参数优化器"""
    
    def __init__(self):
        self.performance_history = []
        self.parameter_history = []
    
    def optimize_parameters(self, recent_performance, current_params):
        """基于近期表现优化参数"""
        # 简单的自适应调整
        if recent_performance < -0.05:  # 如果近期表现差
            # 增加保守性
            current_params['risk_factor'] = max(0.5, current_params.get('risk_factor', 1.0) * 0.9)
            current_params['stop_loss'] = min(0.98, current_params.get('stop_loss', 0.95) + 0.01)
        elif recent_performance > 0.03:  # 如果近期表现好
            # 适度增加激进性
            current_params['risk_factor'] = min(1.2, current_params.get('risk_factor', 1.0) * 1.05)
            current_params['stop_loss'] = max(0.92, current_params.get('stop_loss', 0.95) - 0.005)
        
        return current_params


# 全局变量
market_detector = MarketRegimeDetector()
param_optimizer = DynamicParameterOptimizer()
strategy_params = {
    'risk_factor': 1.0,
    'stop_loss': 0.95,
    'take_profit': 1.08,
    'position_size': 0.8
}


def adaptive_multi_factor_strategy(ctx):
    """
    自适应多因子策略
    
    特点：
    1. 市场状态识别
    2. 动态参数调整
    3. 多时间框架分析
    4. 风险管理优化
    """
    global market_detector, param_optimizer, strategy_params
    
    if ctx.bars < 60:
        return
    
    # 获取数据
    close_prices = pd.Series(ctx.close)
    volume = pd.Series(ctx.volume)
    high_prices = pd.Series(ctx.high)
    low_prices = pd.Series(ctx.low)
    
    # 训练市场状态检测器（仅在开始时）
    if not market_detector.is_trained and len(close_prices) >= 100:
        historical_data = pd.DataFrame({
            'close': close_prices,
            'volume': volume
        })
        market_detector.train_model(historical_data)
    
    # 检测当前市场状态
    market_regime = market_detector.detect_regime(close_prices, volume)
    
    # 计算多时间框架指标
    # 短期指标
    ema_5 = close_prices.ewm(span=5).mean()
    ema_10 = close_prices.ewm(span=10).mean()
    ema_20 = close_prices.ewm(span=20).mean()
    
    # 中期指标
    ema_50 = close_prices.ewm(span=50).mean()
    sma_60 = close_prices.rolling(window=60).mean()
    
    # 动量指标
    rsi = calculate_rsi(close_prices, period=14)
    macd_line, signal_line, _ = calculate_macd(close_prices)
    
    # 波动率指标
    atr = calculate_atr(high_prices, low_prices, close_prices, period=14)
    bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(close_prices, period=20, std_dev=2)
    
    # 成交量指标
    vol_sma = volume.rolling(window=20).mean()
    vol_ratio = volume.iloc[-1] / vol_sma.iloc[-1] if not pd.isna(vol_sma.iloc[-1]) else 1
    
    # 获取当前值
    current_price = close_prices.iloc[-1]
    current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
    current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
    
    # 根据市场状态调整策略
    if market_regime == 0:  # 震荡市场
        score = calculate_range_bound_score(
            current_price, current_rsi, bb_upper.iloc[-1], bb_lower.iloc[-1], 
            bb_middle.iloc[-1], vol_ratio
        )
    elif market_regime == 1:  # 上涨趋势
        score = calculate_trend_following_score(
            ema_5.iloc[-1], ema_10.iloc[-1], ema_20.iloc[-1], ema_50.iloc[-1],
            current_rsi, macd_line.iloc[-1], signal_line.iloc[-1], vol_ratio
        )
    else:  # 下跌趋势
        score = calculate_defensive_score(
            current_price, ema_20.iloc[-1], current_rsi, current_atr, vol_ratio
        )
    
    # 动态调整参数
    recent_performance = calculate_recent_performance(ctx)
    strategy_params = param_optimizer.optimize_parameters(recent_performance, strategy_params)
    
    # 风险管理
    position_size = strategy_params['position_size'] * strategy_params['risk_factor']
    
    # 交易决策
    if score >= 75 and not ctx.long_pos():
        # 强买入信号
        ctx.buy_shares = ctx.calc_target_shares(position_size)
        print(f"{ctx.date} {ctx.symbol}: 自适应策略-买入, 评分={score:.0f}, 市场状态={market_regime}")
    
    elif score <= 25 and ctx.long_pos():
        # 强卖出信号
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: 自适应策略-卖出, 评分={score:.0f}, 市场状态={market_regime}")
    
    # 止损止盈
    elif ctx.long_pos():
        entry_price = ctx.long_pos_price
        current_return = (current_price / entry_price - 1)
        
        # 动态止损
        stop_loss_level = strategy_params['stop_loss']
        if current_atr > 0:
            # 基于ATR的动态止损
            atr_stop = entry_price - (current_atr * 2)
            stop_loss_level = max(stop_loss_level, atr_stop / entry_price)
        
        if current_return <= (stop_loss_level - 1):
            ctx.sell_all_shares()
            print(f"{ctx.date} {ctx.symbol}: 自适应策略-止损, 收益率={current_return:.2%}")
        
        elif current_return >= (strategy_params['take_profit'] - 1):
            # 部分获利了结
            sell_shares = ctx.long_pos_size // 2
            if sell_shares > 0:
                ctx.sell_shares = sell_shares
                print(f"{ctx.date} {ctx.symbol}: 自适应策略-部分获利, 收益率={current_return:.2%}")


def calculate_range_bound_score(price, rsi, bb_upper, bb_lower, bb_middle, vol_ratio):
    """震荡市场评分"""
    score = 50  # 基础分
    
    # RSI超买超卖
    if rsi < 30:
        score += 25  # 超卖买入
    elif rsi > 70:
        score -= 25  # 超买卖出
    
    # 布林带位置
    if price < bb_lower:
        score += 20  # 接近下轨买入
    elif price > bb_upper:
        score -= 20  # 接近上轨卖出
    
    # 成交量确认
    if vol_ratio > 1.2:
        score += 10
    
    return max(0, min(100, score))


def calculate_trend_following_score(ema5, ema10, ema20, ema50, rsi, macd, signal, vol_ratio):
    """趋势跟踪评分"""
    score = 50
    
    # 均线排列
    if ema5 > ema10 > ema20 > ema50:
        score += 30  # 完美多头排列
    elif ema5 > ema10 > ema20:
        score += 20  # 短期多头
    
    # MACD信号
    if macd > signal and macd > 0:
        score += 20
    
    # RSI趋势确认
    if 40 < rsi < 80:
        score += 15
    
    # 成交量确认
    if vol_ratio > 1.1:
        score += 15
    
    return max(0, min(100, score))


def calculate_defensive_score(price, ema20, rsi, atr, vol_ratio):
    """防御性评分"""
    score = 30  # 偏向卖出
    
    # 价格相对位置
    if price < ema20:
        score -= 20
    
    # RSI
    if rsi < 40:
        score -= 15
    
    # 波动率
    if atr > price * 0.02:  # 高波动率
        score -= 15
    
    # 成交量
    if vol_ratio > 1.5:  # 恐慌性抛售
        score -= 20
    
    return max(0, min(100, score))


def calculate_recent_performance(ctx):
    """计算近期表现"""
    if not hasattr(ctx, 'portfolio') or ctx.portfolio is None:
        return 0
    
    try:
        # 简化的近期表现计算
        if len(ctx.portfolio) >= 10:
            recent_return = (ctx.portfolio.iloc[-1] / ctx.portfolio.iloc[-10] - 1)
            return recent_return
    except:
        pass
    
    return 0


def calculate_rsi(prices, period=14):
    """计算RSI"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_macd(prices, fast=12, slow=26, signal=9):
    """计算MACD"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def calculate_atr(high, low, close, period=14):
    """计算ATR"""
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window=period).mean()
    return atr


def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, sma, lower_band
