"""
配置文件 - ETF均线策略回测参数
"""

# ETF代码列表 (可以添加更多ETF)
ETF_SYMBOLS = [
    'SPY',   # SPDR S&P 500 ETF
    'QQQ',   # Invesco QQQ Trust
    'IWM',   # iShares Russell 2000 ETF
    'VTI',   # Vanguard Total Stock Market ETF
]

# 回测参数
BACKTEST_CONFIG = {
    'start_date': '2020-01-01',
    'end_date': '2024-01-01',
    'initial_cash': 100000,  # 初始资金
    'commission': 0.001,     # 手续费率 0.1%
}

# 策略参数
STRATEGY_CONFIG = {
    'short_ma_period': 30,   # 短期均线周期
    'long_ma_period': 60,    # 长期均线周期
    'position_size': 0.95,   # 仓位大小 (95%的资金)
}

# 数据源配置
DATA_CONFIG = {
    'source': 'yfinance',    # 数据源
    'interval': '1d',        # 数据频率 (日线)
}
