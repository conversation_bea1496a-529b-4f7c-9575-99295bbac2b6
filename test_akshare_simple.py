"""
简单测试akshare功能
"""

import akshare as ak
import pandas as pd
from datetime import datetime

def test_basic_akshare():
    """测试akshare基本功能"""
    print("测试AKShare基本功能")
    print("="*40)
    
    try:
        # 1. 测试获取A股历史数据
        print("1. 测试获取A股历史数据...")
        
        # 尝试获取恒生科技ETF(513330)的数据
        symbol = "513330"
        start_date = "20240101"
        end_date = "20240630"
        
        print(f"获取 {symbol} 从 {start_date} 到 {end_date} 的数据...")
        
        data = ak.stock_zh_a_hist(
            symbol=symbol,
            period="daily",
            start_date=start_date,
            end_date=end_date,
            adjust=""
        )
        
        if not data.empty:
            print(f"成功获取数据，共 {len(data)} 条记录")
            print("数据列名:", list(data.columns))
            print("数据预览:")
            print(data.head())
            print()
            
            # 保存数据
            data.to_csv('test_513330_data.csv', index=False, encoding='utf-8-sig')
            print("数据已保存到 test_513330_data.csv")
            
        else:
            print("未获取到数据")
            
    except Exception as e:
        print(f"获取历史数据时出错: {e}")
    
    print("\n" + "="*40)
    
    try:
        # 2. 测试获取实时数据
        print("2. 测试获取A股实时数据...")
        
        # 获取所有A股实时数据（这可能需要一些时间）
        print("正在获取A股实时数据（可能需要一些时间）...")
        spot_data = ak.stock_zh_a_spot_em()
        
        if not spot_data.empty:
            print(f"成功获取实时数据，共 {len(spot_data)} 只股票")
            print("数据列名:", list(spot_data.columns))
            
            # 查找恒生科技ETF
            etf_data = spot_data[spot_data['代码'] == '513330']
            if not etf_data.empty:
                print("\n恒生科技ETF(513330)实时信息:")
                print(etf_data.iloc[0])
            else:
                print("未找到恒生科技ETF(513330)的实时数据")
                
        else:
            print("未获取到实时数据")
            
    except Exception as e:
        print(f"获取实时数据时出错: {e}")
    
    print("\n" + "="*40)
    
    try:
        # 3. 测试其他可能的ETF数据获取方法
        print("3. 测试其他ETF数据获取方法...")
        
        # 尝试使用fund相关函数
        print("尝试获取基金数据...")
        
        # 获取基金基本信息
        fund_info = ak.fund_name_em()
        if not fund_info.empty:
            print(f"获取到 {len(fund_info)} 只基金信息")
            
            # 查找恒生科技相关基金
            hk_tech_funds = fund_info[fund_info['基金简称'].str.contains('恒生科技', na=False)]
            if not hk_tech_funds.empty:
                print("找到恒生科技相关基金:")
                print(hk_tech_funds)
            else:
                print("未找到恒生科技相关基金")
        
    except Exception as e:
        print(f"获取基金数据时出错: {e}")


def test_alternative_methods():
    """测试其他获取ETF数据的方法"""
    print("\n测试其他获取ETF数据的方法")
    print("="*40)
    
    try:
        # 尝试使用不同的时间范围
        print("1. 尝试获取最近30天的数据...")
        
        from datetime import datetime, timedelta
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        data = ak.stock_zh_a_hist(
            symbol="513330",
            period="daily", 
            start_date=start_date.strftime("%Y%m%d"),
            end_date=end_date.strftime("%Y%m%d"),
            adjust=""
        )
        
        if not data.empty:
            print(f"成功获取最近30天数据，共 {len(data)} 条记录")
            print("最新几条数据:")
            print(data.tail())
        else:
            print("未获取到最近30天的数据")
            
    except Exception as e:
        print(f"获取最近数据时出错: {e}")
    
    try:
        # 尝试获取其他知名ETF的数据作为对比
        print("\n2. 尝试获取其他ETF数据作为对比...")
        
        other_etfs = ["510050", "510300", "159915"]  # 50ETF, 300ETF, 创业板ETF
        
        for etf_code in other_etfs:
            try:
                data = ak.stock_zh_a_hist(
                    symbol=etf_code,
                    period="daily",
                    start_date="20240601",
                    end_date="20240610",
                    adjust=""
                )
                
                if not data.empty:
                    print(f"ETF {etf_code}: 成功获取 {len(data)} 条记录")
                else:
                    print(f"ETF {etf_code}: 未获取到数据")
                    
            except Exception as e:
                print(f"ETF {etf_code}: 获取数据出错 - {e}")
                
    except Exception as e:
        print(f"获取对比数据时出错: {e}")


if __name__ == "__main__":
    test_basic_akshare()
    test_alternative_methods()
