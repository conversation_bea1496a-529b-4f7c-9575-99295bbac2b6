"""
简化的测试程序 - 测试pybroker基本功能
"""

import pandas as pd
import numpy as np
import pybroker
from pybroker import Strategy, StrategyConfig, YFinance

from data_loader import DataLoader
from strategy import ma_cross_strategy, get_strategy_info
from config import ETF_SYMBOLS, BACKTEST_CONFIG, STRATEGY_CONFIG

def test_with_sample_data():
    """使用示例数据测试策略"""
    print("ETF均线策略回测测试")
    print("="*50)
    
    # 显示策略信息
    strategy_info = get_strategy_info()
    print("策略信息:")
    for key, value in strategy_info.items():
        print(f"  {key}: {value}")
    print()
    
    # 加载示例数据
    loader = DataLoader()
    try:
        data = loader.load_data('sample_etf_data.csv')
        if data.empty:
            print("没有找到示例数据，正在生成...")
            # 生成示例数据
            sample_data = []
            for symbol in ['SPY']:  # 只测试一个ETF
                df = loader.generate_sample_data(symbol, days=200)
                sample_data.append(df)
            
            data = pd.concat(sample_data, ignore_index=True)
            loader.save_data(data, 'sample_etf_data.csv')
        
        print(f"数据加载成功: {len(data)} 条记录")
        print(f"数据范围: {data['date'].min()} 到 {data['date'].max()}")
        print(f"包含ETF: {data['symbol'].unique()}")
        print()
        
        # 测试单个ETF
        symbol = 'SPY'
        symbol_data = data[data['symbol'] == symbol].copy()
        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
        
        print(f"测试 {symbol} 数据:")
        print(f"  数据点数: {len(symbol_data)}")
        print(f"  价格范围: {symbol_data['close'].min():.2f} - {symbol_data['close'].max():.2f}")
        print()
        
        # 计算移动平均线进行验证
        from strategy import moving_average
        ma30 = moving_average(symbol_data['close'], 30)
        ma60 = moving_average(symbol_data['close'], 60)
        
        print("移动平均线计算验证:")
        print(f"  30日均线最后5个值: {ma30.tail().values}")
        print(f"  60日均线最后5个值: {ma60.tail().values}")
        print()
        
        # 寻找交叉信号
        valid_data = symbol_data[60:].copy()  # 跳过前60天没有完整均线的数据
        ma30_valid = ma30[60:]
        ma60_valid = ma60[60:]
        
        # 检测金叉和死叉
        golden_crosses = []
        death_crosses = []
        
        for i in range(1, len(ma30_valid)):
            prev_short = ma30_valid.iloc[i-1]
            prev_long = ma60_valid.iloc[i-1]
            curr_short = ma30_valid.iloc[i]
            curr_long = ma60_valid.iloc[i]
            
            if prev_short <= prev_long and curr_short > curr_long:
                golden_crosses.append(valid_data.iloc[i]['date'])
            elif prev_short >= prev_long and curr_short < curr_long:
                death_crosses.append(valid_data.iloc[i]['date'])
        
        print(f"发现 {len(golden_crosses)} 个金叉信号")
        print(f"发现 {len(death_crosses)} 个死叉信号")
        
        if golden_crosses:
            print("金叉日期:", golden_crosses[:3])  # 显示前3个
        if death_crosses:
            print("死叉日期:", death_crosses[:3])  # 显示前3个
        
        print("\n基本测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_with_yfinance():
    """使用YFinance测试（如果网络允许）"""
    print("\n尝试使用YFinance获取真实数据...")
    
    try:
        # 启用缓存
        pybroker.enable_data_source_cache('test_cache')
        
        # 创建YFinance数据源
        yfinance = YFinance()
        
        # 获取少量数据进行测试
        df = yfinance.query(['SPY'], start_date='2023-01-01', end_date='2023-06-01')
        
        if not df.empty:
            print(f"成功获取YFinance数据: {len(df)} 条记录")
            print("数据预览:")
            print(df.head())
        else:
            print("YFinance数据为空")
            
    except Exception as e:
        print(f"YFinance测试失败: {e}")

if __name__ == "__main__":
    test_with_sample_data()
    # test_with_yfinance()  # 可选：如果网络允许可以取消注释
