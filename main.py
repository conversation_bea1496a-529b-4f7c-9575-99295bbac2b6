"""
主程序 - ETF均线策略回测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pybroker
from pybroker import Strategy, StrategyConfig

from data_loader import DataLoader
from strategy import ma_cross_strategy, get_strategy_info
from config import ETF_SYMBOLS, BACKTEST_CONFIG, STRATEGY_CONFIG

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class ETFBacktester:
    """ETF均线策略回测器"""
    
    def __init__(self):
        self.data_loader = DataLoader()
        self.data = None
        self.results = {}
        
    def load_data(self, symbols=None, force_reload=False, use_sample=True):
        """加载ETF数据"""
        if symbols is None:
            symbols = ETF_SYMBOLS

        print("正在加载ETF数据...")

        if use_sample:
            # 优先使用示例数据
            try:
                self.data = self.data_loader.load_data('sample_etf_data.csv')
                if not self.data.empty:
                    print(f"使用示例数据，共 {len(self.data)} 条记录")
                    return self.data
            except:
                pass

        # 尝试从本地文件加载
        if not force_reload:
            self.data = self.data_loader.load_data()

        # 如果本地没有数据或强制重新加载，则从网络获取
        if self.data.empty or force_reload:
            print("从网络获取数据...")
            self.data = self.data_loader.fetch_multiple_etfs(symbols)
            self.data_loader.save_data(self.data)

        print(f"数据加载完成，共 {len(self.data)} 条记录")
        return self.data
    
    def run_backtest(self, symbol):
        """运行单个ETF的回测"""
        print(f"\n开始回测 {symbol}...")

        # 筛选特定ETF的数据
        symbol_data = self.data[self.data['symbol'] == symbol].copy()

        if symbol_data.empty:
            print(f"没有找到 {symbol} 的数据")
            return None

        # 确保数据按日期排序
        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)

        print(f"{symbol} 数据范围: {symbol_data['date'].min()} 到 {symbol_data['date'].max()}")
        print(f"数据点数: {len(symbol_data)}")

        # 创建自定义数据源
        from pybroker.data import DataSource

        class CustomDataSource(DataSource):
            def __init__(self, df):
                super().__init__()
                self.df = df

            def query(self, symbols, start_date, end_date, **kwargs):
                # 过滤数据
                filtered_df = self.df[
                    (self.df['date'] >= pd.to_datetime(start_date)) &
                    (self.df['date'] <= pd.to_datetime(end_date))
                ].copy()
                return filtered_df

        # 创建策略配置
        config = StrategyConfig(
            initial_cash=BACKTEST_CONFIG['initial_cash'],
            commission=BACKTEST_CONFIG['commission']
        )

        # 创建策略
        data_source = CustomDataSource(symbol_data)
        strategy = Strategy(
            data_source=data_source,
            start_date=BACKTEST_CONFIG['start_date'],
            end_date=BACKTEST_CONFIG['end_date'],
            config=config
        )

        # 添加策略函数
        strategy.add_execution(ma_cross_strategy, symbols=[symbol])

        # 运行回测
        try:
            result = strategy.backtest()
            print(f"{symbol} 回测完成")
            self.results[symbol] = result
            return result
        except Exception as e:
            print(f"{symbol} 回测失败: {e}")
            return None
    
    def run_all_backtests(self):
        """运行所有ETF的回测"""
        if self.data is None or self.data.empty:
            print("请先加载数据")
            return
        
        symbols = self.data['symbol'].unique()
        print(f"将对以下ETF进行回测: {list(symbols)}")
        
        for symbol in symbols:
            self.run_backtest(symbol)
    
    def print_results(self):
        """打印回测结果"""
        if not self.results:
            print("没有回测结果")
            return
        
        print("\n" + "="*60)
        print("回测结果汇总")
        print("="*60)
        
        strategy_info = get_strategy_info()
        print(f"策略: {strategy_info['name']}")
        print(f"描述: {strategy_info['description']}")
        print(f"回测期间: {BACKTEST_CONFIG['start_date']} 到 {BACKTEST_CONFIG['end_date']}")
        print(f"初始资金: ${BACKTEST_CONFIG['initial_cash']:,.2f}")
        
        print("\n各ETF表现:")
        print("-" * 80)
        print(f"{'ETF':<6} {'总收益率':<10} {'年化收益率':<12} {'最大回撤':<10} {'夏普比率':<10} {'交易次数':<8}")
        print("-" * 80)
        
        for symbol, result in self.results.items():
            if result and hasattr(result, 'stats'):
                stats = result.stats
                total_return = stats.get('Total Return [%]', 0)
                annual_return = stats.get('Annual Return [%]', 0)
                max_drawdown = stats.get('Max Drawdown [%]', 0)
                sharpe_ratio = stats.get('Sharpe Ratio', 0)
                trades = stats.get('# Trades', 0)
                
                print(f"{symbol:<6} {total_return:<10.2f}% {annual_return:<12.2f}% {max_drawdown:<10.2f}% {sharpe_ratio:<10.2f} {trades:<8}")
    
    def plot_results(self, symbol=None):
        """绘制回测结果图表"""
        if not self.results:
            print("没有回测结果可绘制")
            return
        
        if symbol and symbol in self.results:
            symbols_to_plot = [symbol]
        else:
            symbols_to_plot = list(self.results.keys())
        
        fig, axes = plt.subplots(len(symbols_to_plot), 1, figsize=(12, 6*len(symbols_to_plot)))
        if len(symbols_to_plot) == 1:
            axes = [axes]
        
        for i, symbol in enumerate(symbols_to_plot):
            result = self.results[symbol]
            if result and hasattr(result, 'portfolio'):
                portfolio = result.portfolio
                
                axes[i].plot(portfolio.index, portfolio['equity'], label='组合净值', linewidth=2)
                axes[i].set_title(f'{symbol} - 策略表现')
                axes[i].set_xlabel('日期')
                axes[i].set_ylabel('组合价值 ($)')
                axes[i].legend()
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()


def main():
    """主函数"""
    print("ETF均线策略回测系统")
    print("="*50)
    
    # 创建回测器
    backtester = ETFBacktester()
    
    # 加载数据
    try:
        backtester.load_data(ETF_SYMBOLS[:2])  # 先测试前两个ETF
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 运行回测
    try:
        backtester.run_all_backtests()
    except Exception as e:
        print(f"回测运行失败: {e}")
        return
    
    # 显示结果
    backtester.print_results()
    
    # 绘制图表
    try:
        backtester.plot_results()
    except Exception as e:
        print(f"绘图失败: {e}")


if __name__ == "__main__":
    main()
