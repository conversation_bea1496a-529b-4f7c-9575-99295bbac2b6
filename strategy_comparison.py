"""
ETF策略对比测试程序
比较不同策略在相同数据上的表现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pybroker
from pybroker import Strategy, StrategyConfig
from pybroker.data import DataSource
from pybroker.common import FeeMode

from enhanced_strategies import STRATEGY_MAP, get_strategy_list
from strategy import ma_cross_strategy
from config import BACKTEST_CONFIG

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class StrategyComparison:
    """策略对比测试器"""
    
    def __init__(self, data_file='ashare_etf_backtest_data.csv'):
        self.data_file = data_file
        self.data = None
        self.results = {}
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file, encoding='utf-8-sig')
            self.data['date'] = pd.to_datetime(self.data['date'])

            # 检查日期是否有效
            if self.data['date'].isna().any():
                print("警告：数据中存在无效日期")
                self.data = self.data.dropna(subset=['date'])

            print(f"成功加载数据: {len(self.data)} 条记录")
            print(f"包含ETF: {list(self.data['symbol'].unique())}")
            print(f"日期范围: {self.data['date'].min()} 到 {self.data['date'].max()}")
        except Exception as e:
            print(f"数据加载失败: {e}")
            import traceback
            traceback.print_exc()
    
    def run_strategy_comparison(self, symbol='159915', strategies=None):
        """运行策略对比"""
        if self.data is None:
            print("请先加载数据")
            return
        
        if strategies is None:
            strategies = ['original', 'improved_ma', 'rsi_mean_reversion', 'multi_factor']
        
        # 筛选特定ETF的数据
        print(f"\n可用的symbol: {self.data['symbol'].unique()}")
        print(f"symbol类型: {type(self.data['symbol'].iloc[0])}")
        print(f"查找的symbol: {symbol}, 类型: {type(symbol)}")

        # 确保symbol类型匹配
        if isinstance(self.data['symbol'].iloc[0], (int, np.integer)):
            symbol = int(symbol)
        else:
            symbol = str(symbol)

        symbol_data = self.data[self.data['symbol'] == symbol].copy()

        if symbol_data.empty:
            print(f"未找到symbol {symbol} 的数据")
            return

        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)

        print(f"\n开始对比测试 {symbol}")
        print(f"数据范围: {symbol_data['date'].min().date()} 到 {symbol_data['date'].max().date()}")
        print(f"数据点数: {len(symbol_data)}")
        print(f"测试策略: {strategies}")
        
        # 创建数据源
        from ashare_etf_backtest_final import AShareETFDataSource
        data_source = AShareETFDataSource(symbol_data)
        
        for strategy_name in strategies:
            print(f"\n测试策略: {strategy_name}")
            
            try:
                # 创建策略配置
                config = StrategyConfig(
                    initial_cash=BACKTEST_CONFIG['initial_cash'],
                    fee_mode=FeeMode.ORDER_PERCENT,
                    fee_amount=BACKTEST_CONFIG['commission']
                )
                
                # 创建策略
                strategy = Strategy(
                    data_source=data_source,
                    start_date=symbol_data['date'].min().strftime('%Y-%m-%d'),
                    end_date=symbol_data['date'].max().strftime('%Y-%m-%d'),
                    config=config
                )
                
                # 选择策略函数
                if strategy_name == 'original':
                    strategy_func = ma_cross_strategy
                else:
                    strategy_func = STRATEGY_MAP[strategy_name]
                
                # 添加策略函数
                strategy.add_execution(strategy_func, symbols=[symbol])
                
                # 运行回测
                result = strategy.backtest()
                
                if result:
                    self.results[strategy_name] = result
                    print(f"{strategy_name} 回测完成")
                else:
                    print(f"{strategy_name} 回测失败")
                    
            except Exception as e:
                print(f"{strategy_name} 回测出错: {e}")
    
    def print_comparison_results(self):
        """打印对比结果"""
        if not self.results:
            print("没有回测结果")
            return
        
        print("\n" + "="*100)
        print("策略对比结果")
        print("="*100)
        
        strategy_names = get_strategy_list()
        strategy_names['original'] = '原始双均线策略'
        
        print(f"{'策略名称':<20} {'总收益率':<12} {'年化收益率':<12} {'最大回撤':<12} {'夏普比率':<12} {'交易次数':<8}")
        print("-" * 100)
        
        # 收集所有结果用于排序
        results_summary = []
        
        for strategy_name, result in self.results.items():
            display_name = strategy_names.get(strategy_name, strategy_name)[:18]
            
            if result and hasattr(result, 'metrics_df'):
                metrics = result.metrics_df.set_index('name')['value']
                
                total_return = metrics.get('total_return_pct', 0)
                max_drawdown = metrics.get('max_drawdown_pct', 0)
                sharpe_ratio = metrics.get('sharpe_ratio', 0)
                trades = int(metrics.get('trade_count', 0))
                
                # 简化年化收益率计算
                annual_return = total_return * (365 / 500)  # 假设约500个交易日
                
                results_summary.append({
                    'strategy': strategy_name,
                    'display_name': display_name,
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'max_drawdown': max_drawdown,
                    'sharpe_ratio': sharpe_ratio,
                    'trades': trades
                })
        
        # 按总收益率排序
        results_summary.sort(key=lambda x: x['total_return'], reverse=True)
        
        for i, result in enumerate(results_summary):
            rank_symbol = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "  "
            print(f"{rank_symbol}{result['display_name']:<18} {result['total_return']:<12.2f}% "
                  f"{result['annual_return']:<12.2f}% {result['max_drawdown']:<12.2f}% "
                  f"{result['sharpe_ratio']:<12.2f} {result['trades']:<8}")
        
        print("-" * 100)
        
        # 策略特点分析
        print("\n策略特点分析:")
        print("🥇 最佳策略:", results_summary[0]['display_name'])
        print("📈 最高收益:", max(results_summary, key=lambda x: x['total_return'])['display_name'])
        print("🛡️ 最小回撤:", min(results_summary, key=lambda x: abs(x['max_drawdown']))['display_name'])
        print("⚡ 最多交易:", max(results_summary, key=lambda x: x['trades'])['display_name'])
    
    def plot_comparison_results(self):
        """绘制策略对比图表"""
        if not self.results:
            print("没有回测结果可绘制")
            return
        
        strategy_names = get_strategy_list()
        strategy_names['original'] = '原始双均线策略'
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 净值曲线对比
        for strategy_name, result in self.results.items():
            if result and hasattr(result, 'portfolio'):
                portfolio = result.portfolio
                display_name = strategy_names.get(strategy_name, strategy_name)
                ax1.plot(portfolio.index, portfolio['market_value'], 
                        label=display_name, linewidth=2)
        
        ax1.set_title('策略净值曲线对比')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('组合价值 (¥)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 收益率对比
        returns = []
        names = []
        for strategy_name, result in self.results.items():
            if result and hasattr(result, 'metrics_df'):
                metrics = result.metrics_df.set_index('name')['value']
                total_return = metrics.get('total_return_pct', 0)
                returns.append(total_return)
                names.append(strategy_names.get(strategy_name, strategy_name)[:10])
        
        colors = ['green' if r > 0 else 'red' for r in returns]
        ax2.bar(names, returns, color=colors, alpha=0.7)
        ax2.set_title('总收益率对比')
        ax2.set_ylabel('收益率 (%)')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. 最大回撤对比
        drawdowns = []
        for strategy_name, result in self.results.items():
            if result and hasattr(result, 'metrics_df'):
                metrics = result.metrics_df.set_index('name')['value']
                max_drawdown = metrics.get('max_drawdown_pct', 0)
                drawdowns.append(abs(max_drawdown))
        
        ax3.bar(names, drawdowns, color='orange', alpha=0.7)
        ax3.set_title('最大回撤对比')
        ax3.set_ylabel('最大回撤 (%)')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. 交易次数对比
        trades = []
        for strategy_name, result in self.results.items():
            if result and hasattr(result, 'metrics_df'):
                metrics = result.metrics_df.set_index('name')['value']
                trade_count = int(metrics.get('trade_count', 0))
                trades.append(trade_count)
        
        ax4.bar(names, trades, color='blue', alpha=0.7)
        ax4.set_title('交易次数对比')
        ax4.set_ylabel('交易次数')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()


def main():
    """主函数"""
    print("ETF策略对比测试系统")
    print("="*50)
    
    # 显示可用策略
    strategies = get_strategy_list()
    print("可用策略:")
    print("  original: 原始双均线策略")
    for key, name in strategies.items():
        print(f"  {key}: {name}")
    print()
    
    # 创建对比测试器
    comparator = StrategyComparison()
    
    if comparator.data is None:
        print("数据加载失败，请确保 ashare_etf_backtest_data.csv 文件存在")
        return
    
    # 选择要测试的策略
    test_strategies = ['original', 'improved_ma', 'rsi_mean_reversion', 'multi_factor']
    
    # 运行对比测试
    comparator.run_strategy_comparison(symbol='159915', strategies=test_strategies)
    
    # 显示结果
    comparator.print_comparison_results()
    
    # 绘制图表
    try:
        comparator.plot_comparison_results()
    except Exception as e:
        print(f"绘图失败: {e}")


if __name__ == "__main__":
    main()
