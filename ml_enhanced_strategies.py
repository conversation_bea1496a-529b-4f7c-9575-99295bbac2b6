"""
机器学习增强的ETF交易策略
使用随机森林、XGBoost等算法预测价格走势
"""

import pandas as pd
import numpy as np
from pybroker import ExecContext
import warnings
warnings.filterwarnings('ignore')

try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.preprocessing import StandardScaler, RobustScaler
    from sklearn.model_selection import TimeSeriesSplit
    from sklearn.metrics import mean_squared_error
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    print("警告: scikit-learn未安装，将使用简化版本")


class FeatureEngineer:
    """特征工程类"""
    
    @staticmethod
    def create_technical_features(df):
        """创建技术指标特征"""
        features = pd.DataFrame(index=df.index)
        
        # 价格特征
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features['price_change'] = df['close'] - df['close'].shift(1)
        
        # 移动平均特征
        for period in [5, 10, 20, 50]:
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            features[f'price_to_sma_{period}'] = df['close'] / features[f'sma_{period}']
        
        # 波动率特征
        for period in [5, 10, 20]:
            features[f'volatility_{period}'] = features['returns'].rolling(period).std()
            features[f'high_low_ratio_{period}'] = (df['high'] / df['low']).rolling(period).mean()
        
        # 动量特征
        for period in [5, 10, 20]:
            features[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
            features[f'roc_{period}'] = (df['close'] - df['close'].shift(period)) / df['close'].shift(period)
        
        # RSI
        features['rsi_14'] = FeatureEngineer.calculate_rsi(df['close'], 14)
        features['rsi_7'] = FeatureEngineer.calculate_rsi(df['close'], 7)
        
        # MACD
        macd, signal, histogram = FeatureEngineer.calculate_macd(df['close'])
        features['macd'] = macd
        features['macd_signal'] = signal
        features['macd_histogram'] = histogram
        
        # 布林带
        bb_upper, bb_middle, bb_lower = FeatureEngineer.calculate_bollinger_bands(df['close'])
        features['bb_upper'] = bb_upper
        features['bb_middle'] = bb_middle
        features['bb_lower'] = bb_lower
        features['bb_width'] = (bb_upper - bb_lower) / bb_middle
        features['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
        
        # 成交量特征
        features['volume'] = df['volume']
        features['volume_sma_20'] = df['volume'].rolling(20).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma_20']
        features['price_volume'] = df['close'] * df['volume']
        
        # 时间特征
        features['day_of_week'] = df.index.dayofweek
        features['month'] = df.index.month
        features['quarter'] = df.index.quarter
        
        return features
    
    @staticmethod
    def calculate_rsi(prices, period=14):
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        """计算MACD"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    @staticmethod
    def calculate_bollinger_bands(prices, period=20, std_dev=2):
        """计算布林带"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band


class MLPredictor:
    """机器学习预测器"""
    
    def __init__(self, model_type='rf'):
        self.model_type = model_type
        self.model = None
        self.scaler = RobustScaler() if ML_AVAILABLE else None
        self.is_trained = False
        self.feature_importance = None
        
        if ML_AVAILABLE:
            if model_type == 'rf':
                self.model = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42
                )
            elif model_type == 'gb':
                self.model = GradientBoostingRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42
                )
    
    def prepare_data(self, df, target_period=5):
        """准备训练数据"""
        # 创建特征
        features = FeatureEngineer.create_technical_features(df)
        
        # 创建目标变量（未来N天的收益率）
        target = df['close'].shift(-target_period) / df['close'] - 1
        
        # 合并数据
        data = pd.concat([features, target.rename('target')], axis=1)
        data = data.dropna()
        
        if len(data) < 100:
            return None, None
        
        X = data.drop('target', axis=1)
        y = data['target']
        
        return X, y
    
    def train(self, df, target_period=5):
        """训练模型"""
        if not ML_AVAILABLE:
            return False
        
        X, y = self.prepare_data(df, target_period)
        if X is None:
            return False
        
        try:
            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=3)
            scores = []
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # 标准化特征
                X_train_scaled = self.scaler.fit_transform(X_train)
                X_val_scaled = self.scaler.transform(X_val)
                
                # 训练模型
                self.model.fit(X_train_scaled, y_train)
                
                # 验证
                y_pred = self.model.predict(X_val_scaled)
                score = mean_squared_error(y_val, y_pred)
                scores.append(score)
            
            # 使用全部数据重新训练
            X_scaled = self.scaler.fit_transform(X)
            self.model.fit(X_scaled, y)
            
            # 保存特征重要性
            if hasattr(self.model, 'feature_importances_'):
                self.feature_importance = pd.Series(
                    self.model.feature_importances_,
                    index=X.columns
                ).sort_values(ascending=False)
            
            self.is_trained = True
            print(f"模型训练完成，交叉验证MSE: {np.mean(scores):.6f}")
            return True
            
        except Exception as e:
            print(f"模型训练失败: {e}")
            return False
    
    def predict(self, current_features):
        """预测未来收益率"""
        if not self.is_trained or not ML_AVAILABLE:
            return 0
        
        try:
            # 确保特征顺序一致
            if hasattr(self, 'feature_names'):
                current_features = current_features[self.feature_names]
            
            features_scaled = self.scaler.transform(current_features.values.reshape(1, -1))
            prediction = self.model.predict(features_scaled)[0]
            return prediction
        except Exception as e:
            print(f"预测失败: {e}")
            return 0


# 全局ML预测器
ml_predictor = MLPredictor('rf')
ml_gb_predictor = MLPredictor('gb')


def ml_enhanced_strategy(ctx):
    """
    机器学习增强策略
    
    特点：
    1. 使用随机森林预测未来收益
    2. 结合多个ML模型的集成预测
    3. 动态特征重要性分析
    4. 自适应阈值调整
    """
    global ml_predictor, ml_gb_predictor
    
    if ctx.bars < 100:
        return
    
    # 准备数据
    df = pd.DataFrame({
        'close': ctx.close,
        'high': ctx.high,
        'low': ctx.low,
        'volume': ctx.volume
    }, index=pd.to_datetime([ctx.date] * len(ctx.close)))
    
    # 训练模型（仅在开始时）
    if not ml_predictor.is_trained and len(df) >= 150:
        print("开始训练机器学习模型...")
        ml_predictor.train(df, target_period=5)
        if ML_AVAILABLE:
            ml_gb_predictor.train(df, target_period=5)
    
    if not ml_predictor.is_trained:
        return
    
    # 创建当前特征
    features = FeatureEngineer.create_technical_features(df)
    current_features = features.iloc[-1].dropna()
    
    if len(current_features) == 0:
        return
    
    # ML预测
    rf_prediction = ml_predictor.predict(current_features)
    gb_prediction = ml_gb_predictor.predict(current_features) if ML_AVAILABLE else rf_prediction
    
    # 集成预测
    ensemble_prediction = (rf_prediction + gb_prediction) / 2
    
    # 计算置信度
    prediction_std = abs(rf_prediction - gb_prediction) / 2
    confidence = max(0, 1 - prediction_std * 10)  # 简化的置信度计算
    
    # 动态阈值
    base_threshold = 0.02
    buy_threshold = base_threshold * (1 + confidence)
    sell_threshold = -base_threshold * (1 + confidence)
    
    # 当前价格和技术指标
    current_price = ctx.close[-1]
    rsi = FeatureEngineer.calculate_rsi(pd.Series(ctx.close), 14).iloc[-1]
    
    # 交易决策
    position_size = 0.8 * confidence  # 根据置信度调整仓位
    
    if ensemble_prediction > buy_threshold and not ctx.long_pos() and rsi < 80:
        ctx.buy_shares = ctx.calc_target_shares(position_size)
        print(f"{ctx.date} {ctx.symbol}: ML策略-买入, 预测收益={ensemble_prediction:.3f}, 置信度={confidence:.2f}")
    
    elif ensemble_prediction < sell_threshold and ctx.long_pos():
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: ML策略-卖出, 预测收益={ensemble_prediction:.3f}, 置信度={confidence:.2f}")
    
    # 风险管理
    elif ctx.long_pos():
        entry_price = ctx.long_pos_price
        current_return = (current_price / entry_price - 1)
        
        # 动态止损
        if current_return < -0.08:  # 8%止损
            ctx.sell_all_shares()
            print(f"{ctx.date} {ctx.symbol}: ML策略-止损, 收益率={current_return:.2%}")
        
        # 获利了结
        elif current_return > 0.12:  # 12%获利
            sell_shares = ctx.long_pos_size // 2
            if sell_shares > 0:
                ctx.sell_shares = sell_shares
                print(f"{ctx.date} {ctx.symbol}: ML策略-部分获利, 收益率={current_return:.2%}")


def ensemble_meta_strategy(ctx):
    """
    集成元策略
    
    结合多个策略的信号，使用投票机制决策
    """
    if ctx.bars < 60:
        return
    
    # 收集各策略信号
    signals = []
    
    # 信号1: 技术分析信号
    tech_signal = get_technical_signal(ctx)
    signals.append(tech_signal)
    
    # 信号2: 动量信号
    momentum_signal = get_momentum_signal(ctx)
    signals.append(momentum_signal)
    
    # 信号3: 均值回归信号
    mean_reversion_signal = get_mean_reversion_signal(ctx)
    signals.append(mean_reversion_signal)
    
    # 信号4: 成交量信号
    volume_signal = get_volume_signal(ctx)
    signals.append(volume_signal)
    
    # 加权投票
    weights = [0.3, 0.25, 0.25, 0.2]  # 各信号权重
    weighted_signal = sum(s * w for s, w in zip(signals, weights))
    
    # 决策阈值
    if weighted_signal > 0.6 and not ctx.long_pos():
        ctx.buy_shares = ctx.calc_target_shares(0.85)
        print(f"{ctx.date} {ctx.symbol}: 集成策略-买入, 信号强度={weighted_signal:.2f}")
    
    elif weighted_signal < -0.6 and ctx.long_pos():
        ctx.sell_all_shares()
        print(f"{ctx.date} {ctx.symbol}: 集成策略-卖出, 信号强度={weighted_signal:.2f}")


def get_technical_signal(ctx):
    """技术分析信号"""
    close_prices = pd.Series(ctx.close)
    
    # 均线信号
    ema_20 = close_prices.ewm(span=20).mean()
    ema_50 = close_prices.ewm(span=50).mean()
    
    if ema_20.iloc[-1] > ema_50.iloc[-1] and close_prices.iloc[-1] > ema_20.iloc[-1]:
        return 1  # 买入信号
    elif ema_20.iloc[-1] < ema_50.iloc[-1] and close_prices.iloc[-1] < ema_20.iloc[-1]:
        return -1  # 卖出信号
    else:
        return 0  # 中性


def get_momentum_signal(ctx):
    """动量信号"""
    close_prices = pd.Series(ctx.close)
    
    # 计算动量
    momentum_5 = close_prices.iloc[-1] / close_prices.iloc[-6] - 1
    momentum_10 = close_prices.iloc[-1] / close_prices.iloc[-11] - 1
    
    if momentum_5 > 0.02 and momentum_10 > 0.01:
        return 1
    elif momentum_5 < -0.02 and momentum_10 < -0.01:
        return -1
    else:
        return 0


def get_mean_reversion_signal(ctx):
    """均值回归信号"""
    close_prices = pd.Series(ctx.close)
    rsi = FeatureEngineer.calculate_rsi(close_prices, 14)
    
    current_rsi = rsi.iloc[-1]
    
    if current_rsi < 30:
        return 1  # 超卖买入
    elif current_rsi > 70:
        return -1  # 超买卖出
    else:
        return 0


def get_volume_signal(ctx):
    """成交量信号"""
    volume = pd.Series(ctx.volume)
    vol_ma = volume.rolling(20).mean()
    
    vol_ratio = volume.iloc[-1] / vol_ma.iloc[-1]
    
    if vol_ratio > 1.5:  # 成交量放大
        # 结合价格变化判断方向
        price_change = ctx.close[-1] / ctx.close[-2] - 1
        if price_change > 0:
            return 1
        else:
            return -1
    else:
        return 0
